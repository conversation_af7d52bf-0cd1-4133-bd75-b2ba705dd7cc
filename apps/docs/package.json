{"name": "docs", "version": "0.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev -p 3004 --turbo", "start": "next start", "postinstall": "fumadocs-mdx"}, "dependencies": {"next": "15.3.0", "react": "^19.1.0", "react-dom": "^19.1.0", "fumadocs-ui": "15.5.4", "fumadocs-core": "15.5.4", "fumadocs-mdx": "11.6.9"}, "devDependencies": {"@types/node": "22.14.1", "@types/react": "19.1.2", "@types/react-dom": "19.1.2", "typescript": "^5.8.3", "@types/mdx": "^2.0.13", "@tailwindcss/postcss": "^4.1.10", "tailwindcss": "^4.1.4", "postcss": "^8.5.6"}}