import { keys as analytics } from '@repo/analytics/keys';
import { keys as auth } from '@repo/auth/keys';
import { keys as database } from '@repo/database/keys';
import { keys as email } from '@repo/email/keys';
import { keys as flags } from '@repo/feature-flags/keys';
import { keys as core } from '@repo/next-config/keys';
import { keys as notifications } from '@repo/notifications/keys';
import { keys as observability } from '@repo/observability/keys';
import { keys as security } from '@repo/security/keys';
import { keys as webhooks } from '@repo/webhooks/keys';
import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const env = createEnv({
  extends: [
    auth(),
    analytics(),
    core(),
    database(),
    email(),
    flags(),
    notifications(),
    observability(),
    security(),
    webhooks(),
  ],
  server: {},
  client: {
    NEXT_PUBLIC_API_URL: z.string().min(1).url(),
    NEXT_PUBLIC_CORE_BACKEND_URL: z.string().min(1).url(),
    NEXT_PUBLIC_MYINVOIS_API_GENERAL_TIN: z.string().min(1),
    NEXT_PUBLIC_MYINVOIS_API_FOREIGN_BUYER_TIN: z.string().min(1),
    NEXT_PUBLIC_MYINVOIS_API_FOREIGN_SUPPLIER_TIN: z.string().min(1),
    NEXT_PUBLIC_MYINVOIS_API_EXEMPTED_ENTITY_TIN: z.string().min(1),
  },
  runtimeEnv: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
    NEXT_PUBLIC_CORE_BACKEND_URL: process.env.NEXT_PUBLIC_CORE_BACKEND_URL,
    NEXT_PUBLIC_MYINVOIS_API_GENERAL_TIN: process.env.NEXT_PUBLIC_MYINVOIS_API_GENERAL_TIN,
    NEXT_PUBLIC_MYINVOIS_API_FOREIGN_BUYER_TIN: process.env.NEXT_PUBLIC_MYINVOIS_API_FOREIGN_BUYER_TIN,
    NEXT_PUBLIC_MYINVOIS_API_FOREIGN_SUPPLIER_TIN: process.env.NEXT_PUBLIC_MYINVOIS_API_FOREIGN_SUPPLIER_TIN,
    NEXT_PUBLIC_MYINVOIS_API_EXEMPTED_ENTITY_TIN: process.env.NEXT_PUBLIC_MYINVOIS_API_EXEMPTED_ENTITY_TIN,
  },
});
