# E-Invoice App (Next-Forge)

## Commands
- **Build**: `pnpm build` or `turbo build`
- **Dev**: `pnpm dev` (excludes web/docs/portal)
- **Lint**: `pnpm lint` or `ultracite lint`
- **Format**: `pnpm format` or `ultracite format`
- **Test**: `pnpm test` or `turbo test`
- **Single test**: `cd apps/app && npx vitest run __tests__/file.test.tsx`
- **Test watch**: `cd apps/app && npx vitest`
- **Typecheck**: `tsc --noEmit` (in app dirs)
- **Database**: `pnpm migrate` (dev), `pnpm migrate-prod` (production)

## Architecture
Turborepo monorepo with Next.js 15, React 19, TypeScript, Prisma, Better Auth, Biome linting
- **Apps**: `app` (main), `api` (serverless), `web`, `docs`, `portal`
- **Packages**: `auth`, `database`, `design-system`, `ai`, `analytics`, `payments`, etc.

## Code Style
- Uses Biome (ultracite preset) for linting/formatting
- Strict TypeScript with ES2022, NodeNext modules
- Single quotes for JS/JSX (Prettier)
- No console.log (use console.error only)
- Descriptive variable names with auxiliary verbs
- Prefer iteration/modularization over duplication
- Proper error boundaries and user-friendly error messages
